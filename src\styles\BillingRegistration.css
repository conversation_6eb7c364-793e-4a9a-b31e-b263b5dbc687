/* Enhanced Billing Registration Styles - Modern UI */
.billing-registration {
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  max-width: 100%;
  overflow-x: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Mobile-First Responsive Container */
.billing-content-container {
  width: 100%;
  max-width: 100%;
}

.billing-cards-wrapper {
  width: 100%;
  max-width: 100%;
}

.billing-registration .card {
  border: 1px solid #e3e6f0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 15px;
  transition: all 0.2s ease;
  background: #ffffff;
}

.billing-registration .card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  border-color: #d1d3e2;
}

.billing-registration .card-header {
  border-radius: 8px 8px 0 0 !important;
  font-weight: 600;
  padding: 12px 20px;
  border-bottom: 1px solid #e3e6f0;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Header Section */
.billing-registration .billing-header-section .card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
}

.billing-registration .header-title-section h1 {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 5px;
}

.billing-registration .header-actions .btn {
  min-width: 120px;
  font-weight: 600;
  border-radius: 8px;
  padding: 10px 20px;
}

/* Gradient Card Headers */
.billing-registration .bg-gradient-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.billing-registration .bg-gradient-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.billing-registration .bg-gradient-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.billing-registration .card-body {
  padding: 20px;
}

/* Enhanced Form Styling */
.billing-registration .form-label {
  font-weight: 600;
  color: #5a5c69;
  margin-bottom: 6px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.billing-registration .form-control,
.billing-registration .form-select {
  border: 1px solid #d1d3e2;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: #ffffff;
  height: 38px;
}

.billing-registration .form-control-lg,
.billing-registration .form-select-lg {
  padding: 14px 18px;
  font-size: 15px;
  border-radius: 12px;
}

.billing-registration .form-control:focus,
.billing-registration .form-select:focus {
  border-color: #4e73df;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.1);
  background-color: #ffffff;
  outline: none;
}

.billing-registration .form-control:hover,
.billing-registration .form-select:hover {
  border-color: #a2a8b4;
}

.billing-registration .input-group-text {
  background-color: #e9ecef;
  border: 2px solid #e9ecef;
  border-radius: 8px 0 0 8px;
  font-weight: 600;
}

/* Searchable Dropdown */
.searchable-dropdown {
  position: relative;
}

.searchable-dropdown .dropdown-menu {
  border: 2px solid #007bff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.searchable-dropdown .dropdown-item {
  padding: 10px 15px;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.searchable-dropdown .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.searchable-dropdown .dropdown-item:focus {
  background-color: #007bff;
  color: white;
}

/* Table Styling */
.billing-registration .table {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
  margin-bottom: 1rem;
  color: #5a5c69;
  border-collapse: collapse;
}

.billing-registration .table thead th {
  background-color: #f8f9fc;
  color: #4e73df;
  font-weight: 600;
  border-bottom: 1px solid #e3e6f0;
  padding: 12px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.billing-registration .table tbody td {
  padding: 10px 12px;
  vertical-align: middle;
  border-top: 1px solid #e3e6f0;
  font-size: 14px;
}

.billing-registration .table tbody tr:hover {
  background-color: #f8f9fc;
}

.billing-registration .table tfoot th {
  background-color: #eaecf4;
  color: #5a5c69;
  font-weight: 600;
  border-top: 1px solid #e3e6f0;
  padding: 12px;
  font-size: 13px;
}

/* Billing Summary */
.billing-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.billing-summary .fw-bold {
  font-size: 16px;
}

.billing-summary .text-primary {
  color: #007bff !important;
  font-size: 18px;
}

.billing-summary .text-danger {
  color: #dc3545 !important;
  font-size: 16px;
}

/* Button Styling */
.billing-registration .btn {
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.billing-registration .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.billing-registration .btn-primary {
  background-color: #4e73df;
  border-color: #4e73df;
  color: #ffffff;
}

.billing-registration .btn-primary:hover {
  background-color: #2e59d9;
  border-color: #2e59d9;
}

.billing-registration .btn-success {
  background-color: #1cc88a;
  border-color: #1cc88a;
  color: #ffffff;
}

.billing-registration .btn-success:hover {
  background-color: #17a673;
  border-color: #17a673;
}

.billing-registration .btn-danger {
  background-color: #e74a3b;
  border-color: #e74a3b;
  color: #ffffff;
}

.billing-registration .btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}

.billing-registration .btn-warning {
  background-color: #f6c23e;
  border-color: #f6c23e;
  color: #212529;
}

.billing-registration .btn-warning:hover {
  background-color: #dda20a;
  border-color: #dda20a;
}

.billing-registration .btn-info {
  background-color: #36b9cc;
  border-color: #36b9cc;
  color: #ffffff;
}

.billing-registration .btn-info:hover {
  background-color: #258391;
  border-color: #258391;
}

.billing-registration .btn-secondary {
  background-color: #858796;
  border-color: #858796;
  color: #ffffff;
}

.billing-registration .btn-secondary:hover {
  background-color: #717384;
  border-color: #717384;
}

/* Header Styling */
.billing-registration h1 {
  color: #5a5c69;
  font-weight: 700;
  margin-bottom: 0;
  font-size: 24px;
}

.billing-registration h6 {
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Modern Input Group Styling */
.billing-registration .input-group-text {
  background-color: #f8f9fc;
  border: 1px solid #d1d3e2;
  border-radius: 4px 0 0 4px;
  font-weight: 500;
  color: #5a5c69;
  font-size: 13px;
}

.billing-registration .input-group .form-control {
  border-left: 0;
  border-radius: 0 4px 4px 0;
}

.billing-registration .input-group .form-control:focus {
  border-color: #4e73df;
  box-shadow: none;
}

.billing-registration .input-group .form-control:focus + .input-group-text,
.billing-registration .input-group .input-group-text:has(+ .form-control:focus) {
  border-color: #4e73df;
}

/* Alert Styling */
.billing-registration .alert {
  border-radius: 4px;
  border: 1px solid transparent;
  padding: 12px 16px;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 14px;
}

.billing-registration .alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.billing-registration .alert-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.billing-registration .alert-warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.billing-registration .alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Modal Styling */
.billing-registration .modal-content {
  border-radius: 6px;
  border: 1px solid #e3e6f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.billing-registration .modal-header {
  border-radius: 6px 6px 0 0;
  border-bottom: 1px solid #e3e6f0;
  padding: 16px 20px;
  background-color: #f8f9fc;
}

.billing-registration .modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #5a5c69;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.billing-registration .modal-body {
  padding: 20px;
}

.billing-registration .modal-footer {
  border-radius: 0 0 6px 6px;
  border-top: 1px solid #e3e6f0;
  padding: 16px 20px;
  background-color: #f8f9fc;
}

/* Mobile-First Responsive Design */
/* Desktop: 1024px+ */
@media (min-width: 1024px) {
  .billing-registration {
    padding: 20px;
    overflow-x: hidden;
  }

  .billing-content-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  .billing-cards-wrapper {
    width: 100%;
  }

  .billing-registration .card {
    margin-bottom: 20px;
  }
}

/* Tablet: 768px-1024px */
@media (max-width: 1024px) and (min-width: 769px) {
  .billing-registration {
    padding: 15px;
    overflow-x: hidden;
  }

  .billing-content-container {
    width: 100%;
    max-width: 100%;
  }

  .billing-cards-wrapper {
    width: 100%;
  }
}

/* Mobile: 320px-768px */
@media (max-width: 768px) {
  .billing-registration {
    padding: 10px;
    overflow-x: hidden;
  }

  .billing-content-container {
    width: 100%;
    padding: 0;
  }

  .billing-cards-wrapper {
    width: 100%;
  }

  .billing-registration .card {
    margin-bottom: 15px;
    border-radius: 8px;
  }

  .billing-registration .card-body {
    padding: 15px;
  }

  .billing-registration .btn {
    padding: 8px 16px;
    font-size: 14px;
    min-width: auto;
  }

  .billing-registration h1 {
    font-size: 24px;
  }

  .billing-summary {
    padding: 15px;
  }

  /* Ensure no horizontal scrolling */
  .billing-registration .row {
    margin-left: 0;
    margin-right: 0;
  }

  .billing-registration .col,
  .billing-registration [class*="col-"] {
    padding-left: 8px;
    padding-right: 8px;
  }
}

/* Small Mobile: 320px-576px */
@media (max-width: 576px) {
  .billing-registration {
    padding: 8px;
  }

  .billing-content-container {
    padding: 0;
  }

  .billing-registration .card {
    margin-bottom: 12px;
    border-radius: 6px;
  }

  .billing-registration .card-body {
    padding: 12px;
  }

  .billing-registration .d-flex.justify-content-between {
    flex-direction: column;
    gap: 10px;
  }

  .billing-registration .d-flex.justify-content-end {
    flex-direction: column;
  }

  .billing-registration .btn {
    width: 100%;
    margin-bottom: 10px;
    padding: 10px 12px;
    font-size: 13px;
  }

  .billing-registration .form-control,
  .billing-registration .form-select {
    font-size: 13px;
    padding: 8px 10px;
  }

  .billing-registration h1 {
    font-size: 20px;
  }

  .billing-registration h6 {
    font-size: 14px;
  }

  /* Ensure touch targets are at least 44px */
  .billing-registration .btn,
  .billing-registration .form-control,
  .billing-registration .form-select {
    min-height: 44px;
  }
}

/* Loading State */
.billing-registration .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Form Validation */
.billing-registration .form-control:invalid {
  border-color: #dc3545;
}

.billing-registration .form-control:valid {
  border-color: #28a745;
}

/* Custom Checkbox Styling */
.billing-registration .form-check-input {
  width: 1.2em;
  height: 1.2em;
  margin-top: 0.1em;
}

.billing-registration .form-check-label {
  font-weight: 500;
  color: #495057;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.billing-registration .card {
  animation: fadeIn 0.5s ease-out;
}

/* Enhanced Discount System Styles */
.billing-registration .discount-section {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.billing-registration .discount-section .form-label {
  color: #856404;
  font-weight: 600;
}

/* Payment Status Styles */
.billing-registration .payment-status-paid {
  background-color: #d4edda;
  color: #155724;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 600;
}

.billing-registration .payment-status-partial {
  background-color: #fff3cd;
  color: #856404;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 600;
}

.billing-registration .payment-status-pending {
  background-color: #f8d7da;
  color: #721c24;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 600;
}

/* Age Input Styling */
.billing-registration .age-input-group {
  display: flex;
  gap: 10px;
  align-items: end;
}

.billing-registration .age-input-group .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* Title and Patient Type Styling */
.billing-registration .patient-info-row {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

/* Mother's Name Field for Baby/Infant */
.billing-registration .mother-name-section {
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.billing-registration .mother-name-section .form-label {
  color: #0056b3;
  font-weight: 600;
}

/* Referral Source Styling */
.billing-registration .referral-section {
  background-color: #f0f8f0;
  border: 1px solid #c3e6c3;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

/* Enhanced Button Styling */
.billing-registration .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.billing-registration .btn-danger:hover:not(:disabled) {
  background: linear-gradient(45deg, #c82333, #a71e2a);
}

/* Compact Form Layout */
.billing-registration .compact-form .form-group {
  margin-bottom: 0.75rem;
}

.billing-registration .compact-form .form-label {
  font-size: 12px;
  margin-bottom: 4px;
  font-weight: 600;
  color: #5a5c69;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.billing-registration .compact-form .form-control,
.billing-registration .compact-form .form-select {
  padding: 6px 10px;
  font-size: 13px;
  height: 32px;
  border: 1px solid #d1d3e2;
  border-radius: 3px;
}

.billing-registration .compact-form .row {
  margin-left: -8px;
  margin-right: -8px;
}

.billing-registration .compact-form .col,
.billing-registration .compact-form [class*="col-"] {
  padding-left: 8px;
  padding-right: 8px;
}

/* Print Styles */
@media print {
  .billing-registration .btn,
  .billing-registration .card-header {
    display: none !important;
  }

  .billing-registration .card {
    box-shadow: none;
    border: 1px solid #000;
  }

  .billing-registration {
    background-color: white;
  }

  .billing-registration .discount-section,
  .billing-registration .mother-name-section,
  .billing-registration .referral-section {
    background-color: white !important;
    border: 1px solid #000 !important;
  }
}

/* Enhanced Patient Search Styles */
.billing-registration .patient-search-section {
  background: #ffffff;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e3e6f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Enhanced Row Spacing */
.billing-registration .g-4 {
  --bs-gutter-x: 2rem;
  --bs-gutter-y: 2rem;
}

.billing-registration .g-3 {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 1.5rem;
}

/* Card Header Icons */
.billing-registration .card-header .fa-icon {
  margin-right: 8px;
  opacity: 0.9;
}

/* Form Text Styling */
.billing-registration .form-text {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 4px;
}

/* Enhanced Button Group */
.billing-registration .search-mode-toggle .btn-group {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.billing-registration .search-mode-toggle .btn {
  border: none;
  padding: 16px 24px;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s ease;
  position: relative;
}

.billing-registration .search-mode-toggle .btn:first-child {
  border-radius: 12px 0 0 12px;
}

.billing-registration .search-mode-toggle .btn:last-child {
  border-radius: 0 12px 12px 0;
}

.billing-registration .search-mode-toggle .btn-group {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.billing-registration .search-mode-toggle .btn {
  border: none;
  padding: 12px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.billing-registration .search-mode-toggle .btn:first-child {
  border-radius: 8px 0 0 8px;
}

.billing-registration .search-mode-toggle .btn:last-child {
  border-radius: 0 8px 8px 0;
}

.billing-registration .search-mode-toggle .btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  transform: scale(1.02);
}

.billing-registration .search-mode-toggle .btn-outline-primary {
  background: white;
  color: #007bff;
  border: 2px solid #007bff;
}

.billing-registration .search-mode-toggle .btn-outline-primary:hover {
  background: #007bff;
  color: white;
  transform: scale(1.02);
}

/* Selected Patient Display */
.billing-registration .selected-patient-display {
  background: #e7f3ff;
  border: 1px solid #4e73df;
  border-radius: 4px;
  padding: 12px;
  margin: 10px 0;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.billing-registration .selected-patient-display .patient-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 8px;
}

.billing-registration .selected-patient-display .patient-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #666;
  font-size: 0.9rem;
}

.billing-registration .selected-patient-display .patient-detail-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Search Results Styling */
.billing-registration .search-results-container {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 15px;
}

.billing-registration .search-results-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.billing-registration .patient-search-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.billing-registration .patient-search-item:last-child {
  border-bottom: none;
}

.billing-registration .patient-search-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-left: 4px solid #007bff;
  padding-left: 16px;
  transform: translateX(4px);
}

.billing-registration .patient-search-item .patient-name {
  font-weight: 600;
  color: #212529;
  margin-bottom: 5px;
}

.billing-registration .patient-search-item .patient-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #6c757d;
  font-size: 0.85rem;
}

.billing-registration .patient-search-item .info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Mobile Search Specific Styles */
.billing-registration .mobile-search-input {
  position: relative;
}

.billing-registration .mobile-search-input .form-control {
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
  letter-spacing: 1px;
  padding: 12px 15px;
}

.billing-registration .mobile-search-status {
  margin-top: 10px;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.billing-registration .mobile-search-status.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #28a745;
  color: #155724;
}

.billing-registration .mobile-search-status.error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 1px solid #dc3545;
  color: #721c24;
}

/* No Results Styling */
.billing-registration .no-results-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  margin-top: 15px;
}

.billing-registration .no-results-container .no-results-icon {
  color: #6c757d;
  margin-bottom: 15px;
}

.billing-registration .no-results-container .no-results-text {
  color: #6c757d;
  margin-bottom: 20px;
  font-size: 1rem;
}

.billing-registration .no-results-container .btn {
  padding: 12px 24px;
  font-weight: 600;
  border-radius: 8px;
}

/* Enhanced Input Group Styling */
.billing-registration .enhanced-input-group .input-group-text {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  border-right: none;
  color: #495057;
  font-weight: 600;
}

.billing-registration .enhanced-input-group .form-control {
  border: 2px solid #dee2e6;
  border-left: none;
}

.billing-registration .enhanced-input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.billing-registration .enhanced-input-group .form-control:focus + .input-group-text,
.billing-registration .enhanced-input-group .input-group-text:has(+ .form-control:focus) {
  border-color: #007bff;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .billing-registration .search-mode-toggle .btn {
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .billing-registration .patient-search-item {
    padding: 12px 15px;
  }

  .billing-registration .patient-search-item .patient-info {
    flex-direction: column;
    gap: 8px;
  }

  .billing-registration .selected-patient-display .patient-details {
    flex-direction: column;
    gap: 8px;
  }

  .billing-registration .no-results-container {
    padding: 20px 15px;
  }
}

@media (max-width: 576px) {
  .billing-registration .search-mode-toggle .btn-group {
    width: 100%;
  }

  .billing-registration .search-mode-toggle .btn {
    flex: 1;
    padding: 12px 10px;
    font-size: 0.85rem;
  }

  .billing-registration .mobile-search-input .form-control {
    font-size: 1rem;
  }
}


@media (prefers-color-scheme: dark) {
  .comprehensive-dashboard {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .card {
    background-color:black;
    border-color: #404040;
  }
  
  .table {
    color: #ffffff;
  }
  
  .table-light {
    background-color: #404040;
    color: #ffffff;
  }
  
  .text-muted {
    color: #adb5bd !important;
  }
  
  .bg-light {
    background-color: #2d2d2d !important;
  }
}
